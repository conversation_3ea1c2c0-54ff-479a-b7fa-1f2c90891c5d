package org.example.agent;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.example.model.User;
import org.example.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Google ADK Server Agent that provides AI-powered user management capabilities
 * This agent wraps the UserService and provides intelligent responses for user operations
 */
@Component
public class GoogleAdkServerAgent {
    
    private final UserService userService;
    private final UserOperationsAgent userOperationsAgent;
    private final ObjectMapper objectMapper;
    
    @Autowired
    public GoogleAdkServerAgent(UserService userService, UserOperationsAgent userOperationsAgent) {
        this.userService = userService;
        this.userOperationsAgent = userOperationsAgent;
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * Process a natural language request and execute the appropriate user operation
     */
    public Map<String, Object> processRequest(String request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // Analyze the request to determine the operation
            String operation = analyzeRequest(request);
            
            switch (operation.toLowerCase()) {
                case "list_users":
                case "get_all_users":
                case "show_users":
                    response = handleListUsers();
                    break;
                    
                case "get_user":
                case "find_user":
                case "show_user":
                    response = handleGetUser(request);
                    break;
                    
                case "create_user":
                case "add_user":
                case "new_user":
                    response = handleCreateUser(request);
                    break;
                    
                case "update_user":
                case "modify_user":
                case "edit_user":
                    response = handleUpdateUser(request);
                    break;
                    
                case "delete_user":
                case "remove_user":
                    response = handleDeleteUser(request);
                    break;
                    
                default:
                    response.put("status", "error");
                    response.put("message", "Unknown operation: " + operation);
                    response.put("suggestion", "Try: list users, get user by id, create user, update user, or delete user");
            }
            
        } catch (Exception e) {
            response.put("status", "error");
            response.put("message", "Error processing request: " + e.getMessage());
        }
        
        return response;
    }
    
    private String analyzeRequest(String request) {
        String lowerRequest = request.toLowerCase();
        
        if (lowerRequest.contains("list") || lowerRequest.contains("all") || lowerRequest.contains("show all")) {
            return "list_users";
        } else if (lowerRequest.contains("get") || lowerRequest.contains("find") || lowerRequest.contains("show")) {
            return "get_user";
        } else if (lowerRequest.contains("create") || lowerRequest.contains("add") || lowerRequest.contains("new")) {
            return "create_user";
        } else if (lowerRequest.contains("update") || lowerRequest.contains("modify") || lowerRequest.contains("edit")) {
            return "update_user";
        } else if (lowerRequest.contains("delete") || lowerRequest.contains("remove")) {
            return "delete_user";
        }
        
        return "unknown";
    }
    
    private Map<String, Object> handleListUsers() {
        Map<String, Object> response = new HashMap<>();
        List<User> users = userService.findAll();
        
        response.put("status", "success");
        response.put("operation", "list_users");
        response.put("data", users);
        response.put("message", "Found " + users.size() + " users");
        
        return response;
    }
    
    private Map<String, Object> handleGetUser(String request) {
        Map<String, Object> response = new HashMap<>();
        
        // Extract user ID from request
        Long userId = extractUserId(request);
        if (userId == null) {
            response.put("status", "error");
            response.put("message", "Please specify a user ID");
            return response;
        }
        
        Optional<User> user = userService.findById(userId);
        if (user.isPresent()) {
            response.put("status", "success");
            response.put("operation", "get_user");
            response.put("data", user.get());
            response.put("message", "User found");
        } else {
            response.put("status", "error");
            response.put("message", "User with ID " + userId + " not found");
        }
        
        return response;
    }
    
    private Map<String, Object> handleCreateUser(String request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // Extract user data from request
            Map<String, String> userData = extractUserData(request);
            
            if (!userData.containsKey("name") || !userData.containsKey("email")) {
                response.put("status", "error");
                response.put("message", "Please provide both name and email");
                return response;
            }
            
            User newUser = new User(null, userData.get("name"), userData.get("email"));
            User savedUser = userService.save(newUser);
            
            response.put("status", "success");
            response.put("operation", "create_user");
            response.put("data", savedUser);
            response.put("message", "User created successfully");
            
        } catch (Exception e) {
            response.put("status", "error");
            response.put("message", "Error creating user: " + e.getMessage());
        }
        
        return response;
    }
    
    private Map<String, Object> handleUpdateUser(String request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            Long userId = extractUserId(request);
            if (userId == null) {
                response.put("status", "error");
                response.put("message", "Please specify a user ID");
                return response;
            }
            
            Optional<User> existingUser = userService.findById(userId);
            if (!existingUser.isPresent()) {
                response.put("status", "error");
                response.put("message", "User with ID " + userId + " not found");
                return response;
            }
            
            Map<String, String> userData = extractUserData(request);
            User user = existingUser.get();
            
            if (userData.containsKey("name")) {
                user.setName(userData.get("name"));
            }
            if (userData.containsKey("email")) {
                user.setEmail(userData.get("email"));
            }
            
            User updatedUser = userService.save(user);
            
            response.put("status", "success");
            response.put("operation", "update_user");
            response.put("data", updatedUser);
            response.put("message", "User updated successfully");
            
        } catch (Exception e) {
            response.put("status", "error");
            response.put("message", "Error updating user: " + e.getMessage());
        }
        
        return response;
    }
    
    private Map<String, Object> handleDeleteUser(String request) {
        Map<String, Object> response = new HashMap<>();
        
        Long userId = extractUserId(request);
        if (userId == null) {
            response.put("status", "error");
            response.put("message", "Please specify a user ID");
            return response;
        }
        
        Optional<User> user = userService.findById(userId);
        if (!user.isPresent()) {
            response.put("status", "error");
            response.put("message", "User with ID " + userId + " not found");
            return response;
        }
        
        userService.deleteById(userId);
        
        response.put("status", "success");
        response.put("operation", "delete_user");
        response.put("message", "User deleted successfully");
        
        return response;
    }
    
    private Long extractUserId(String request) {
        // Simple regex to extract numbers from the request
        String[] words = request.split("\\s+");
        for (String word : words) {
            try {
                return Long.parseLong(word);
            } catch (NumberFormatException e) {
                // Continue searching
            }
        }
        return null;
    }
    
    private Map<String, String> extractUserData(String request) {
        Map<String, String> userData = new HashMap<>();
        
        // Simple extraction logic - in a real implementation, you'd use NLP
        String[] parts = request.split("\\s+");
        
        for (int i = 0; i < parts.length - 1; i++) {
            String current = parts[i].toLowerCase();
            String next = parts[i + 1];
            
            if (current.equals("name") || current.equals("named")) {
                userData.put("name", next);
            } else if (current.equals("email")) {
                userData.put("email", next);
            }
        }
        
        return userData;
    }
}
