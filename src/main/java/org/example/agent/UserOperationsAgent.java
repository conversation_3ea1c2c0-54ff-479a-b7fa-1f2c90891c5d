package org.example.agent;

import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Specialized agent for user operations with AI-powered capabilities
 * This agent provides intelligent suggestions and validation for user operations
 */
@Component
public class UserOperationsAgent {
    
    /**
     * Validate user data and provide suggestions
     */
    public Map<String, Object> validateUserData(String name, String email) {
        Map<String, Object> validation = new HashMap<>();
        boolean isValid = true;
        StringBuilder suggestions = new StringBuilder();
        
        // Validate name
        if (name == null || name.trim().isEmpty()) {
            isValid = false;
            suggestions.append("Name is required. ");
        } else if (name.length() < 2) {
            isValid = false;
            suggestions.append("Name should be at least 2 characters long. ");
        }
        
        // Validate email
        if (email == null || email.trim().isEmpty()) {
            isValid = false;
            suggestions.append("Email is required. ");
        } else if (!isValidEmail(email)) {
            isValid = false;
            suggestions.append("Please provide a valid email address. ");
        }
        
        validation.put("isValid", isValid);
        validation.put("suggestions", suggestions.toString().trim());
        
        return validation;
    }
    
    /**
     * Generate intelligent suggestions for user operations
     */
    public Map<String, Object> generateSuggestions(String operation, Map<String, Object> context) {
        Map<String, Object> suggestions = new HashMap<>();
        
        switch (operation.toLowerCase()) {
            case "create_user":
                suggestions.put("tips", "Consider adding additional fields like phone number or address for a complete profile");
                suggestions.put("validation", "Make sure the email is unique and the name is properly formatted");
                break;
                
            case "update_user":
                suggestions.put("tips", "You can update individual fields without affecting others");
                suggestions.put("validation", "Verify the user exists before attempting to update");
                break;
                
            case "delete_user":
                suggestions.put("tips", "Consider archiving users instead of permanent deletion for audit purposes");
                suggestions.put("validation", "Make sure to handle any related data before deletion");
                break;
                
            case "search_user":
                suggestions.put("tips", "You can search by ID, name, or email");
                suggestions.put("validation", "Use partial matches for name searches");
                break;
                
            default:
                suggestions.put("tips", "Available operations: create, read, update, delete users");
        }
        
        return suggestions;
    }
    
    /**
     * Analyze user request and extract intent
     */
    public Map<String, Object> analyzeIntent(String request) {
        Map<String, Object> intent = new HashMap<>();
        String lowerRequest = request.toLowerCase();
        
        // Determine confidence level
        double confidence = 0.0;
        String operation = "unknown";
        
        if (lowerRequest.contains("create") || lowerRequest.contains("add") || lowerRequest.contains("new")) {
            operation = "create_user";
            confidence = 0.9;
        } else if (lowerRequest.contains("update") || lowerRequest.contains("modify") || lowerRequest.contains("edit")) {
            operation = "update_user";
            confidence = 0.9;
        } else if (lowerRequest.contains("delete") || lowerRequest.contains("remove")) {
            operation = "delete_user";
            confidence = 0.9;
        } else if (lowerRequest.contains("get") || lowerRequest.contains("find") || lowerRequest.contains("show")) {
            if (lowerRequest.contains("all") || lowerRequest.contains("list")) {
                operation = "list_users";
            } else {
                operation = "get_user";
            }
            confidence = 0.8;
        }
        
        intent.put("operation", operation);
        intent.put("confidence", confidence);
        intent.put("originalRequest", request);
        
        return intent;
    }
    
    /**
     * Provide contextual help based on the current operation
     */
    public String getContextualHelp(String operation) {
        switch (operation.toLowerCase()) {
            case "create_user":
                return "To create a user, provide: 'create user name [Name] email [Email]'";
                
            case "update_user":
                return "To update a user, provide: 'update user [ID] name [NewName] email [NewEmail]'";
                
            case "delete_user":
                return "To delete a user, provide: 'delete user [ID]'";
                
            case "get_user":
                return "To get a specific user, provide: 'get user [ID]'";
                
            case "list_users":
                return "To list all users, say: 'list users' or 'show all users'";
                
            default:
                return "Available commands: create user, update user, delete user, get user, list users";
        }
    }
    
    /**
     * Format response for better user experience
     */
    public Map<String, Object> formatResponse(Map<String, Object> rawResponse) {
        Map<String, Object> formatted = new HashMap<>(rawResponse);
        
        // Add user-friendly messages
        if ("success".equals(rawResponse.get("status"))) {
            String operation = (String) rawResponse.get("operation");
            switch (operation) {
                case "create_user":
                    formatted.put("friendlyMessage", "✅ Great! I've successfully created the new user.");
                    break;
                case "update_user":
                    formatted.put("friendlyMessage", "✅ Perfect! The user information has been updated.");
                    break;
                case "delete_user":
                    formatted.put("friendlyMessage", "✅ Done! The user has been removed from the system.");
                    break;
                case "get_user":
                    formatted.put("friendlyMessage", "✅ Found the user! Here are the details.");
                    break;
                case "list_users":
                    formatted.put("friendlyMessage", "✅ Here are all the users in the system.");
                    break;
            }
        } else {
            formatted.put("friendlyMessage", "❌ " + rawResponse.get("message"));
        }
        
        return formatted;
    }
    
    private boolean isValidEmail(String email) {
        return email != null && email.contains("@") && email.contains(".");
    }
}
