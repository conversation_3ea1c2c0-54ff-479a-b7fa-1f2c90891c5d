package org.example;

import org.example.agent.GoogleAdkServerAgent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * REST Controller that exposes the Google ADK Server Agent functionality
 */
@RestController
@RequestMapping("/api/agent")
@CrossOrigin(origins = "*")
public class AgentController {
    
    private final GoogleAdkServerAgent serverAgent;
    
    @Autowired
    public AgentController(GoogleAdkServerAgent serverAgent) {
        this.serverAgent = serverAgent;
    }
    
    /**
     * Process natural language requests for user operations
     */
    @PostMapping("/process")
    public ResponseEntity<Map<String, Object>> processRequest(@RequestBody Map<String, String> request) {
        String userRequest = request.get("request");
        
        if (userRequest == null || userRequest.trim().isEmpty()) {
            return ResponseEntity.badRequest().body(Map.of(
                "status", "error",
                "message", "Request cannot be empty"
            ));
        }
        
        Map<String, Object> response = serverAgent.processRequest(userRequest);
        return ResponseEntity.ok(response);
    }
    
    /**
     * Health check endpoint for the agent
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        return ResponseEntity.ok(Map.of(
            "status", "healthy",
            "service", "Google ADK Server Agent",
            "message", "Agent is running and ready to process requests"
        ));
    }
}
