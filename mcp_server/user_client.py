import requests
import json
from typing import Dict, List, Optional, Any

class UserApiClient:
    """Client to communicate with the Java Spring Boot User API"""
    
    def __init__(self, base_url: str = "http://localhost:8080"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
    
    def process_agent_request(self, request: str) -> Dict[str, Any]:
        """Send a natural language request to the Google ADK Server Agent"""
        try:
            response = self.session.post(
                f"{self.base_url}/api/agent/process",
                json={"request": request}
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            return {
                "status": "error",
                "message": f"Failed to communicate with server agent: {str(e)}"
            }
    
    def get_all_users(self) -> Dict[str, Any]:
        """Get all users from the API"""
        try:
            response = self.session.get(f"{self.base_url}/api/users")
            response.raise_for_status()
            users = response.json()
            return {
                "status": "success",
                "data": users,
                "message": f"Retrieved {len(users)} users"
            }
        except requests.exceptions.RequestException as e:
            return {
                "status": "error",
                "message": f"Failed to retrieve users: {str(e)}"
            }
    
    def get_user_by_id(self, user_id: int) -> Dict[str, Any]:
        """Get a specific user by ID"""
        try:
            response = self.session.get(f"{self.base_url}/api/users/{user_id}")
            if response.status_code == 404:
                return {
                    "status": "error",
                    "message": f"User with ID {user_id} not found"
                }
            response.raise_for_status()
            user = response.json()
            return {
                "status": "success",
                "data": user,
                "message": "User retrieved successfully"
            }
        except requests.exceptions.RequestException as e:
            return {
                "status": "error",
                "message": f"Failed to retrieve user: {str(e)}"
            }
    
    def create_user(self, name: str, email: str) -> Dict[str, Any]:
        """Create a new user"""
        try:
            user_data = {"name": name, "email": email}
            response = self.session.post(
                f"{self.base_url}/api/users",
                json=user_data
            )
            response.raise_for_status()
            user = response.json()
            return {
                "status": "success",
                "data": user,
                "message": "User created successfully"
            }
        except requests.exceptions.RequestException as e:
            return {
                "status": "error",
                "message": f"Failed to create user: {str(e)}"
            }
    
    def update_user(self, user_id: int, name: Optional[str] = None, email: Optional[str] = None) -> Dict[str, Any]:
        """Update an existing user"""
        try:
            # First get the current user data
            current_user_response = self.get_user_by_id(user_id)
            if current_user_response["status"] == "error":
                return current_user_response
            
            current_user = current_user_response["data"]
            
            # Update only provided fields
            user_data = {
                "name": name if name is not None else current_user["name"],
                "email": email if email is not None else current_user["email"]
            }
            
            response = self.session.put(
                f"{self.base_url}/api/users/{user_id}",
                json=user_data
            )
            if response.status_code == 404:
                return {
                    "status": "error",
                    "message": f"User with ID {user_id} not found"
                }
            response.raise_for_status()
            user = response.json()
            return {
                "status": "success",
                "data": user,
                "message": "User updated successfully"
            }
        except requests.exceptions.RequestException as e:
            return {
                "status": "error",
                "message": f"Failed to update user: {str(e)}"
            }
    
    def delete_user(self, user_id: int) -> Dict[str, Any]:
        """Delete a user by ID"""
        try:
            response = self.session.delete(f"{self.base_url}/api/users/{user_id}")
            if response.status_code == 404:
                return {
                    "status": "error",
                    "message": f"User with ID {user_id} not found"
                }
            response.raise_for_status()
            return {
                "status": "success",
                "message": "User deleted successfully"
            }
        except requests.exceptions.RequestException as e:
            return {
                "status": "error",
                "message": f"Failed to delete user: {str(e)}"
            }
    
    def health_check(self) -> Dict[str, Any]:
        """Check if the API is healthy"""
        try:
            response = self.session.get(f"{self.base_url}/api/agent/health")
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            return {
                "status": "error",
                "message": f"Health check failed: {str(e)}"
            }
