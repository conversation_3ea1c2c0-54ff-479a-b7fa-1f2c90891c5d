#!/usr/bin/env python3
"""
MCP Server for User Management with Google ADK Server Agent Integration
"""

import asyncio
import json
from typing import Any, Dict, List, Optional
from mcp.server import Server
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
    LoggingLevel
)
from user_client import UserApiClient

# Initialize the user API client
user_client = UserApiClient()

# Create MCP server instance
server = Server("user-management-server")

@server.list_resources()
async def list_resources() -> List[Resource]:
    """List available resources"""
    return [
        Resource(
            uri="users://all",
            name="All Users",
            description="List of all users in the system",
            mimeType="application/json"
        ),
        Resource(
            uri="users://health",
            name="System Health",
            description="Health status of the user management system",
            mimeType="application/json"
        )
    ]

@server.read_resource()
async def read_resource(uri: str) -> str:
    """Read a specific resource"""
    if uri == "users://all":
        result = user_client.get_all_users()
        return json.dumps(result, indent=2)
    elif uri == "users://health":
        result = user_client.health_check()
        return json.dumps(result, indent=2)
    else:
        raise ValueError(f"Unknown resource: {uri}")

@server.list_tools()
async def list_tools() -> List[Tool]:
    """List available tools"""
    return [
        Tool(
            name="process_user_request",
            description="Process natural language requests for user operations using Google ADK Server Agent",
            inputSchema={
                "type": "object",
                "properties": {
                    "request": {
                        "type": "string",
                        "description": "Natural language request for user operations (e.g., 'create user named John <NAME_EMAIL>')"
                    }
                },
                "required": ["request"]
            }
        ),
        Tool(
            name="list_users",
            description="Get all users in the system",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        ),
        Tool(
            name="get_user",
            description="Get a specific user by ID",
            inputSchema={
                "type": "object",
                "properties": {
                    "user_id": {
                        "type": "integer",
                        "description": "The ID of the user to retrieve"
                    }
                },
                "required": ["user_id"]
            }
        ),
        Tool(
            name="create_user",
            description="Create a new user",
            inputSchema={
                "type": "object",
                "properties": {
                    "name": {
                        "type": "string",
                        "description": "The name of the user"
                    },
                    "email": {
                        "type": "string",
                        "description": "The email address of the user"
                    }
                },
                "required": ["name", "email"]
            }
        ),
        Tool(
            name="update_user",
            description="Update an existing user",
            inputSchema={
                "type": "object",
                "properties": {
                    "user_id": {
                        "type": "integer",
                        "description": "The ID of the user to update"
                    },
                    "name": {
                        "type": "string",
                        "description": "The new name of the user (optional)"
                    },
                    "email": {
                        "type": "string",
                        "description": "The new email address of the user (optional)"
                    }
                },
                "required": ["user_id"]
            }
        ),
        Tool(
            name="delete_user",
            description="Delete a user by ID",
            inputSchema={
                "type": "object",
                "properties": {
                    "user_id": {
                        "type": "integer",
                        "description": "The ID of the user to delete"
                    }
                },
                "required": ["user_id"]
            }
        )
    ]

@server.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """Handle tool calls"""
    
    if name == "process_user_request":
        request = arguments.get("request", "")
        result = user_client.process_agent_request(request)
        
        return [TextContent(
            type="text",
            text=json.dumps(result, indent=2)
        )]
    
    elif name == "list_users":
        result = user_client.get_all_users()
        
        return [TextContent(
            type="text",
            text=json.dumps(result, indent=2)
        )]
    
    elif name == "get_user":
        user_id = arguments.get("user_id")
        if not user_id:
            return [TextContent(
                type="text",
                text=json.dumps({"status": "error", "message": "user_id is required"}, indent=2)
            )]
        
        result = user_client.get_user_by_id(user_id)
        
        return [TextContent(
            type="text",
            text=json.dumps(result, indent=2)
        )]
    
    elif name == "create_user":
        name_val = arguments.get("name")
        email = arguments.get("email")
        
        if not name_val or not email:
            return [TextContent(
                type="text",
                text=json.dumps({"status": "error", "message": "name and email are required"}, indent=2)
            )]
        
        result = user_client.create_user(name_val, email)
        
        return [TextContent(
            type="text",
            text=json.dumps(result, indent=2)
        )]
    
    elif name == "update_user":
        user_id = arguments.get("user_id")
        name_val = arguments.get("name")
        email = arguments.get("email")
        
        if not user_id:
            return [TextContent(
                type="text",
                text=json.dumps({"status": "error", "message": "user_id is required"}, indent=2)
            )]
        
        result = user_client.update_user(user_id, name_val, email)
        
        return [TextContent(
            type="text",
            text=json.dumps(result, indent=2)
        )]
    
    elif name == "delete_user":
        user_id = arguments.get("user_id")
        
        if not user_id:
            return [TextContent(
                type="text",
                text=json.dumps({"status": "error", "message": "user_id is required"}, indent=2)
            )]
        
        result = user_client.delete_user(user_id)
        
        return [TextContent(
            type="text",
            text=json.dumps(result, indent=2)
        )]
    
    else:
        return [TextContent(
            type="text",
            text=json.dumps({"status": "error", "message": f"Unknown tool: {name}"}, indent=2)
        )]

async def main():
    """Main entry point for the MCP server"""
    async with stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            server.create_initialization_options()
        )

if __name__ == "__main__":
    asyncio.run(main())
