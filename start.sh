#!/bin/bash

echo "🚀 Starting User Management System with Google ADK and MCP"
echo "=========================================================="

# Function to check if a port is in use
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null ; then
        echo "❌ Port $1 is already in use"
        return 1
    else
        echo "✅ Port $1 is available"
        return 0
    fi
}

# Check required ports
echo "🔍 Checking required ports..."
check_port 8080 || exit 1
check_port 8081 || exit 1
check_port 8501 || exit 1

# Check Java version
echo "☕ Checking Java version..."
if command -v java &> /dev/null; then
    JAVA_VERSION=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}')
    echo "✅ Java version: $JAVA_VERSION"
else
    echo "❌ Java not found. Please install Java 17 or higher."
    exit 1
fi

# Check Python version
echo "🐍 Checking Python version..."
if command -v python3 &> /dev/null; then
    PYTHON_VERSION=$(python3 --version)
    echo "✅ $PYTHON_VERSION"
else
    echo "❌ Python3 not found. Please install Python 3.11 or higher."
    exit 1
fi

echo ""
echo "🏗️  Building and starting services..."
echo ""

# Start Java Spring Boot application
echo "1️⃣  Starting Java Spring Boot Application..."
./gradlew bootRun &
JAVA_PID=$!
echo "   Java app started with PID: $JAVA_PID"

# Wait for Java app to start
echo "   Waiting for Java app to be ready..."
for i in {1..30}; do
    if curl -s http://localhost:8080/api/agent/health > /dev/null 2>&1; then
        echo "   ✅ Java app is ready!"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "   ❌ Java app failed to start within 30 seconds"
        kill $JAVA_PID 2>/dev/null
        exit 1
    fi
    sleep 1
done

# Start MCP Server
echo ""
echo "2️⃣  Starting MCP Server..."
cd mcp_server
python3 -m venv venv 2>/dev/null || true
source venv/bin/activate 2>/dev/null || true
pip install -r requirements.txt > /dev/null 2>&1
python3 server.py &
MCP_PID=$!
cd ..
echo "   MCP server started with PID: $MCP_PID"

# Start Streamlit application
echo ""
echo "3️⃣  Starting Streamlit Application..."
cd streamlit_app
python3 -m venv venv 2>/dev/null || true
source venv/bin/activate 2>/dev/null || true
pip install -r requirements.txt > /dev/null 2>&1
streamlit run app.py --server.port=8501 --server.address=0.0.0.0 &
STREAMLIT_PID=$!
cd ..
echo "   Streamlit app started with PID: $STREAMLIT_PID"

echo ""
echo "🎉 All services started successfully!"
echo ""
echo "📱 Access your applications:"
echo "   🌐 Streamlit Web App: http://localhost:8501"
echo "   🔧 Java API: http://localhost:8080"
echo "   🔗 MCP Server: http://localhost:8081"
echo ""
echo "💡 Try these natural language commands in the Streamlit app:"
echo "   • 'List all users'"
echo "   • 'Create user named Alice <NAME_EMAIL>'"
echo "   • 'Show user 1'"
echo "   • 'Update user 1 name Bob'"
echo "   • 'Delete user 2'"
echo ""
echo "🛑 To stop all services, press Ctrl+C"

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Stopping all services..."
    kill $JAVA_PID 2>/dev/null
    kill $MCP_PID 2>/dev/null
    kill $STREAMLIT_PID 2>/dev/null
    echo "✅ All services stopped"
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Wait for user to stop the script
wait
