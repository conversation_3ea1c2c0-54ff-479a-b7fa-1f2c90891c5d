# User Management System with Google ADK and MCP

A comprehensive user management system featuring:
- **Google ADK Server Agent** (Java/Spring Boot) - Intelligent server-side agent for user operations
- **MCP Server** (Python) - Model Context Protocol server exposing user operations
- **Streamlit Client** (Python) - Web application with client-side ADK agent

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Streamlit     │    │   MCP Server    │    │  Spring Boot    │
│  Client Agent   │◄──►│   (Python)      │◄──►│ Server Agent    │
│   (Python)      │    │                 │    │    (Java)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
        │                        │                        │
        │                        │                        │
        ▼                        ▼                        ▼
   Web Interface          MCP Protocol              REST API + 
   Natural Language       Tool Calls               In-Memory Storage
   Processing                                      
```

## Features

### Google ADK Server Agent (Java)
- **Natural Language Processing**: Understands user requests in plain English
- **Intelligent Operations**: Processes CRUD operations with context awareness
- **REST API**: Exposes `/api/agent/process` endpoint for natural language requests
- **User Management**: Full CRUD operations on User entities stored in memory

### MCP Server (Python)
- **Protocol Compliance**: Implements Model Context Protocol for tool exposure
- **Tool Integration**: Exposes user management tools to MCP clients
- **HTTP Bridge**: Connects MCP protocol to REST API
- **Resource Management**: Provides structured access to user data

### Streamlit Client (Python)
- **Natural Language Interface**: Chat-like interface for user interactions
- **Client ADK Agent**: Intelligent client-side processing and suggestions
- **Dashboard**: Real-time user data visualization
- **Manual Operations**: Traditional form-based CRUD operations

## Quick Start

### Prerequisites
- Java 17+
- Python 3.11+
- Docker & Docker Compose (optional)

### Option 1: Docker Compose (Recommended)

```bash
# Clone and navigate to the project
cd MCPTool

# Start all services
docker-compose up --build

# Access the applications:
# - Streamlit App: http://localhost:8501
# - Java API: http://localhost:8080
# - MCP Server: http://localhost:8081
```

### Option 2: Manual Setup

#### 1. Start the Java Spring Boot Application

```bash
# Build and run the Java application
./gradlew bootRun

# The API will be available at http://localhost:8080
# Test with: curl http://localhost:8080/api/agent/health
```

#### 2. Start the MCP Server

```bash
cd mcp_server

# Install dependencies
pip install -r requirements.txt

# Run the MCP server
python server.py

# The MCP server will be available at http://localhost:8081
```

#### 3. Start the Streamlit Application

```bash
cd streamlit_app

# Install dependencies
pip install -r requirements.txt

# Run the Streamlit app
streamlit run app.py

# The web app will be available at http://localhost:8501
```

## Usage Examples

### Natural Language Commands

The system understands natural language commands like:

- **List Users**: "Show me all users", "List users", "What users do we have?"
- **Get User**: "Show user 1", "Get details for user 2", "Find user 3"
- **Create User**: "Create user named Alice <NAME_EMAIL>"
- **Update User**: "Update user 1 name Bob", "Change user 2 email <EMAIL>"
- **Delete User**: "Delete user 1", "Remove user 2"

### API Endpoints

#### Google ADK Server Agent
- `POST /api/agent/process` - Process natural language requests
- `GET /api/agent/health` - Health check

#### Traditional REST API
- `GET /api/users` - List all users
- `GET /api/users/{id}` - Get user by ID
- `POST /api/users` - Create user
- `PUT /api/users/{id}` - Update user
- `DELETE /api/users/{id}` - Delete user

### MCP Tools

The MCP server exposes these tools:
- `process_user_request` - Natural language processing
- `list_users` - Get all users
- `get_user` - Get user by ID
- `create_user` - Create new user
- `update_user` - Update existing user
- `delete_user` - Delete user

## Project Structure

```
MCPTool/
├── src/main/java/org/example/
│   ├── agent/                     # Google ADK Server Agent
│   │   ├── GoogleAdkServerAgent.java
│   │   └── UserOperationsAgent.java
│   ├── config/                    # Configuration
│   │   └── AgentConfig.java
│   ├── model/                     # Data models
│   │   └── User.java
│   ├── service/                   # Business logic
│   │   └── UserService.java
│   ├── AgentController.java       # Agent REST controller
│   ├── UserRestController.java    # User REST controller
│   └── Main.java                  # Spring Boot main class
├── mcp_server/                    # MCP Server (Python)
│   ├── server.py                  # Main MCP server
│   ├── user_client.py            # HTTP client for Java API
│   └── requirements.txt
├── streamlit_app/                 # Streamlit Client (Python)
│   ├── app.py                     # Main Streamlit application
│   ├── client_agent.py           # Client ADK agent
│   ├── mcp_client.py             # MCP client
│   └── requirements.txt
├── build.gradle.kts              # Gradle build file
├── docker-compose.yml            # Docker composition
└── README.md                     # This file
```

## Development

### Adding New Features

1. **Server Agent**: Extend `GoogleAdkServerAgent.java` for new operations
2. **MCP Server**: Add new tools in `server.py`
3. **Client Agent**: Enhance `ClientAdkAgent.py` for better NLP
4. **UI**: Modify `app.py` for new interface elements

### Testing

```bash
# Test Java application
./gradlew test

# Test MCP server
cd mcp_server
python -m pytest

# Test Streamlit app
cd streamlit_app
streamlit run app.py
```

## Troubleshooting

### Common Issues

1. **Port Conflicts**: Ensure ports 8080, 8081, and 8501 are available
2. **Java Version**: Requires Java 17 or higher
3. **Python Dependencies**: Use virtual environments to avoid conflicts
4. **MCP Communication**: Ensure the Java API is running before starting MCP server

### Logs

- **Java App**: Check console output or `logs/` directory
- **MCP Server**: Check stdout for MCP protocol messages
- **Streamlit**: Check browser console and terminal output

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
