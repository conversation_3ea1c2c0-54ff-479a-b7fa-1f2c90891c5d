#!/usr/bin/env python3
"""
Test script to verify the User Management System with Google ADK and MCP
"""

import requests
import json
import time
import sys

def test_java_api():
    """Test the Java Spring Boot API"""
    print("🧪 Testing Java Spring Boot API...")
    
    try:
        # Test health endpoint
        response = requests.get("http://localhost:8080/api/agent/health")
        if response.status_code == 200:
            print("   ✅ Health check passed")
        else:
            print(f"   ❌ Health check failed: {response.status_code}")
            return False
        
        # Test natural language processing
        test_request = {
            "request": "list all users"
        }
        response = requests.post("http://localhost:8080/api/agent/process", json=test_request)
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Natural language processing works: {result.get('status')}")
        else:
            print(f"   ❌ Natural language processing failed: {response.status_code}")
            return False
        
        # Test user creation
        test_request = {
            "request": "create user named TestUser <NAME_EMAIL>"
        }
        response = requests.post("http://localhost:8080/api/agent/process", json=test_request)
        if response.status_code == 200:
            result = response.json()
            if result.get('status') == 'success':
                print("   ✅ User creation works")
                return True
            else:
                print(f"   ❌ User creation failed: {result.get('message')}")
                return False
        else:
            print(f"   ❌ User creation request failed: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("   ❌ Cannot connect to Java API. Is it running on port 8080?")
        return False
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
        return False

def test_traditional_api():
    """Test the traditional REST API"""
    print("🧪 Testing Traditional REST API...")
    
    try:
        # Test get all users
        response = requests.get("http://localhost:8080/api/users")
        if response.status_code == 200:
            users = response.json()
            print(f"   ✅ Get all users works: Found {len(users)} users")
        else:
            print(f"   ❌ Get all users failed: {response.status_code}")
            return False
        
        # Test create user
        new_user = {
            "name": "API Test User",
            "email": "<EMAIL>"
        }
        response = requests.post("http://localhost:8080/api/users", json=new_user)
        if response.status_code == 200:
            created_user = response.json()
            user_id = created_user.get('id')
            print(f"   ✅ Create user works: Created user with ID {user_id}")
            
            # Test get specific user
            response = requests.get(f"http://localhost:8080/api/users/{user_id}")
            if response.status_code == 200:
                print("   ✅ Get specific user works")
            else:
                print(f"   ❌ Get specific user failed: {response.status_code}")
                return False
            
            # Test update user
            updated_user = {
                "name": "Updated API Test User",
                "email": "<EMAIL>"
            }
            response = requests.put(f"http://localhost:8080/api/users/{user_id}", json=updated_user)
            if response.status_code == 200:
                print("   ✅ Update user works")
            else:
                print(f"   ❌ Update user failed: {response.status_code}")
                return False
            
            # Test delete user
            response = requests.delete(f"http://localhost:8080/api/users/{user_id}")
            if response.status_code == 204:
                print("   ✅ Delete user works")
                return True
            else:
                print(f"   ❌ Delete user failed: {response.status_code}")
                return False
        else:
            print(f"   ❌ Create user failed: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("   ❌ Cannot connect to Java API. Is it running on port 8080?")
        return False
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
        return False

def test_streamlit_app():
    """Test if Streamlit app is accessible"""
    print("🧪 Testing Streamlit Application...")
    
    try:
        response = requests.get("http://localhost:8501")
        if response.status_code == 200:
            print("   ✅ Streamlit app is accessible")
            return True
        else:
            print(f"   ❌ Streamlit app returned status: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("   ❌ Cannot connect to Streamlit app. Is it running on port 8501?")
        return False
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Testing User Management System with Google ADK and MCP")
    print("=" * 60)
    
    # Wait a moment for services to be ready
    print("⏳ Waiting for services to be ready...")
    time.sleep(2)
    
    tests_passed = 0
    total_tests = 3
    
    # Run tests
    if test_java_api():
        tests_passed += 1
    
    print()
    
    if test_traditional_api():
        tests_passed += 1
    
    print()
    
    if test_streamlit_app():
        tests_passed += 1
    
    print()
    print("=" * 60)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Your system is working correctly.")
        print()
        print("🌐 Access your applications:")
        print("   • Streamlit Web App: http://localhost:8501")
        print("   • Java API Documentation: http://localhost:8080/api/agent/health")
        print()
        print("💡 Try these commands in the Streamlit app:")
        print("   • 'List all users'")
        print("   • 'Create user named Alice <NAME_EMAIL>'")
        print("   • 'Show user 1'")
        print("   • 'Update user 1 name Bob'")
        print("   • 'Delete user 2'")
        return 0
    else:
        print("❌ Some tests failed. Please check the services and try again.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
