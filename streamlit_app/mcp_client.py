import json
import subprocess
import asyncio
from typing import Dict, Any, List, Optional

class MCPClient:
    """Client to communicate with the MCP server"""
    
    def __init__(self, server_path: str = "../mcp_server/server.py"):
        self.server_path = server_path
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Call a tool on the MCP server"""
        try:
            # Create the MCP request
            request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": arguments
                }
            }
            
            # Start the MCP server process
            process = await asyncio.create_subprocess_exec(
                "python", self.server_path,
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            # Send the request
            request_json = json.dumps(request) + "\n"
            stdout, stderr = await process.communicate(request_json.encode())
            
            if process.returncode != 0:
                return {
                    "status": "error",
                    "message": f"MCP server error: {stderr.decode()}"
                }
            
            # Parse the response
            response_lines = stdout.decode().strip().split('\n')
            for line in response_lines:
                if line.strip():
                    try:
                        response = json.loads(line)
                        if "result" in response:
                            # Extract the text content from the MCP response
                            content = response["result"]["content"]
                            if content and len(content) > 0:
                                text_content = content[0]["text"]
                                return json.loads(text_content)
                    except json.JSONDecodeError:
                        continue
            
            return {
                "status": "error",
                "message": "No valid response from MCP server"
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"Failed to communicate with MCP server: {str(e)}"
            }
    
    def call_tool_sync(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Synchronous wrapper for call_tool"""
        return asyncio.run(self.call_tool(tool_name, arguments))
    
    def process_user_request(self, request: str) -> Dict[str, Any]:
        """Process a natural language request using the Google ADK Server Agent"""
        return self.call_tool_sync("process_user_request", {"request": request})
    
    def list_users(self) -> Dict[str, Any]:
        """Get all users"""
        return self.call_tool_sync("list_users", {})
    
    def get_user(self, user_id: int) -> Dict[str, Any]:
        """Get a specific user by ID"""
        return self.call_tool_sync("get_user", {"user_id": user_id})
    
    def create_user(self, name: str, email: str) -> Dict[str, Any]:
        """Create a new user"""
        return self.call_tool_sync("create_user", {"name": name, "email": email})
    
    def update_user(self, user_id: int, name: Optional[str] = None, email: Optional[str] = None) -> Dict[str, Any]:
        """Update an existing user"""
        arguments = {"user_id": user_id}
        if name:
            arguments["name"] = name
        if email:
            arguments["email"] = email
        return self.call_tool_sync("update_user", arguments)
    
    def delete_user(self, user_id: int) -> Dict[str, Any]:
        """Delete a user"""
        return self.call_tool_sync("delete_user", {"user_id": user_id})
