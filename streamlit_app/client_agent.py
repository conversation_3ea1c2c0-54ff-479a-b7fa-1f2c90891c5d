from typing import Dict, Any, List, Optional
import re

class ClientAdkAgent:
    """Client-side Google ADK Agent for intelligent user interactions"""
    
    def __init__(self, mcp_client):
        self.mcp_client = mcp_client
        self.conversation_history = []
    
    def process_natural_language_request(self, user_input: str) -> Dict[str, Any]:
        """Process natural language input and execute appropriate actions"""
        
        # Add to conversation history
        self.conversation_history.append({
            "type": "user_input",
            "content": user_input,
            "timestamp": self._get_timestamp()
        })
        
        # Analyze the intent
        intent = self._analyze_intent(user_input)
        
        # Execute the appropriate action
        if intent["confidence"] > 0.7:
            result = self._execute_intent(intent, user_input)
        else:
            # Use the server agent for complex requests
            result = self.mcp_client.process_user_request(user_input)
        
        # Add result to conversation history
        self.conversation_history.append({
            "type": "agent_response",
            "content": result,
            "timestamp": self._get_timestamp()
        })
        
        return result
    
    def _analyze_intent(self, user_input: str) -> Dict[str, Any]:
        """Analyze user intent from natural language input"""
        user_input_lower = user_input.lower()
        
        # Define intent patterns
        patterns = {
            "list_users": [
                r"(list|show|display|get)\s+(all\s+)?users",
                r"show\s+me\s+(all\s+)?users",
                r"what\s+users\s+do\s+we\s+have"
            ],
            "get_user": [
                r"(get|show|find)\s+user\s+(\d+)",
                r"show\s+me\s+user\s+(\d+)",
                r"details\s+for\s+user\s+(\d+)"
            ],
            "create_user": [
                r"(create|add|new)\s+user",
                r"add\s+a\s+new\s+user",
                r"register\s+user"
            ],
            "update_user": [
                r"(update|modify|edit)\s+user\s+(\d+)",
                r"change\s+user\s+(\d+)",
                r"edit\s+user\s+(\d+)"
            ],
            "delete_user": [
                r"(delete|remove)\s+user\s+(\d+)",
                r"remove\s+user\s+(\d+)"
            ]
        }
        
        # Check patterns
        for intent, pattern_list in patterns.items():
            for pattern in pattern_list:
                match = re.search(pattern, user_input_lower)
                if match:
                    return {
                        "intent": intent,
                        "confidence": 0.9,
                        "extracted_data": match.groups() if match.groups() else [],
                        "original_input": user_input
                    }
        
        return {
            "intent": "unknown",
            "confidence": 0.0,
            "extracted_data": [],
            "original_input": user_input
        }
    
    def _execute_intent(self, intent: Dict[str, Any], user_input: str) -> Dict[str, Any]:
        """Execute the identified intent"""
        
        intent_name = intent["intent"]
        extracted_data = intent["extracted_data"]
        
        try:
            if intent_name == "list_users":
                return self.mcp_client.list_users()
            
            elif intent_name == "get_user":
                if extracted_data and len(extracted_data) > 0:
                    user_id = int(extracted_data[-1])  # Get the last number found
                    return self.mcp_client.get_user(user_id)
                else:
                    return {
                        "status": "error",
                        "message": "Please specify a user ID"
                    }
            
            elif intent_name == "create_user":
                # Extract name and email from the input
                name, email = self._extract_user_data(user_input)
                if name and email:
                    return self.mcp_client.create_user(name, email)
                else:
                    return {
                        "status": "error",
                        "message": "Please provide both name and email for the new user"
                    }
            
            elif intent_name == "update_user":
                if extracted_data and len(extracted_data) > 0:
                    user_id = int(extracted_data[-1])
                    name, email = self._extract_user_data(user_input)
                    return self.mcp_client.update_user(user_id, name, email)
                else:
                    return {
                        "status": "error",
                        "message": "Please specify a user ID"
                    }
            
            elif intent_name == "delete_user":
                if extracted_data and len(extracted_data) > 0:
                    user_id = int(extracted_data[-1])
                    return self.mcp_client.delete_user(user_id)
                else:
                    return {
                        "status": "error",
                        "message": "Please specify a user ID"
                    }
            
            else:
                return {
                    "status": "error",
                    "message": "Unknown intent"
                }
                
        except Exception as e:
            return {
                "status": "error",
                "message": f"Error executing intent: {str(e)}"
            }
    
    def _extract_user_data(self, user_input: str) -> tuple:
        """Extract name and email from user input"""
        name = None
        email = None
        
        # Extract email using regex
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        email_match = re.search(email_pattern, user_input)
        if email_match:
            email = email_match.group()
        
        # Extract name (simple approach - look for patterns)
        name_patterns = [
            r'name\s+([A-Za-z\s]+?)(?:\s+email|\s+with|\s*$)',
            r'named\s+([A-Za-z\s]+?)(?:\s+email|\s+with|\s*$)',
            r'called\s+([A-Za-z\s]+?)(?:\s+email|\s+with|\s*$)'
        ]
        
        for pattern in name_patterns:
            name_match = re.search(pattern, user_input, re.IGNORECASE)
            if name_match:
                name = name_match.group(1).strip()
                break
        
        return name, email
    
    def get_suggestions(self, current_input: str) -> List[str]:
        """Provide intelligent suggestions based on current input"""
        suggestions = []
        
        if not current_input:
            suggestions = [
                "List all users",
                "Create a new user",
                "Show user details",
                "Update user information",
                "Delete a user"
            ]
        else:
            input_lower = current_input.lower()
            
            if "list" in input_lower or "show" in input_lower:
                suggestions.append("List all users")
                suggestions.append("Show user 1")
                
            if "create" in input_lower or "add" in input_lower:
                suggestions.append("Create user named John <NAME_EMAIL>")
                suggestions.append("Add new user")
                
            if "update" in input_lower or "edit" in input_lower:
                suggestions.append("Update user 1 name Alice")
                suggestions.append("Edit user 2 email <EMAIL>")
                
            if "delete" in input_lower or "remove" in input_lower:
                suggestions.append("Delete user 1")
                suggestions.append("Remove user 2")
        
        return suggestions[:5]  # Return top 5 suggestions
    
    def get_conversation_summary(self) -> Dict[str, Any]:
        """Get a summary of the conversation"""
        total_interactions = len([h for h in self.conversation_history if h["type"] == "user_input"])
        successful_operations = len([
            h for h in self.conversation_history 
            if h["type"] == "agent_response" and 
            isinstance(h["content"], dict) and 
            h["content"].get("status") == "success"
        ])
        
        return {
            "total_interactions": total_interactions,
            "successful_operations": successful_operations,
            "conversation_length": len(self.conversation_history)
        }
    
    def _get_timestamp(self) -> str:
        """Get current timestamp"""
        from datetime import datetime
        return datetime.now().isoformat()
