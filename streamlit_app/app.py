import streamlit as st
import pandas as pd
import plotly.express as px
import json
from datetime import datetime
from mcp_client import MC<PERSON>lient
from client_agent import ClientAdkAgent

# Page configuration
st.set_page_config(
    page_title="User Management with Google ADK",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state
if 'mcp_client' not in st.session_state:
    st.session_state.mcp_client = MCPClient()

if 'client_agent' not in st.session_state:
    st.session_state.client_agent = ClientAdkAgent(st.session_state.mcp_client)

if 'chat_history' not in st.session_state:
    st.session_state.chat_history = []

# Main title
st.title("🤖 User Management with Google ADK")
st.markdown("*Intelligent user management powered by Google ADK Server Agent and MCP*")

# Sidebar
with st.sidebar:
    st.header("🛠️ Operations")
    
    # Quick actions
    st.subheader("Quick Actions")
    
    if st.button("📋 List All Users", use_container_width=True):
        result = st.session_state.mcp_client.list_users()
        st.session_state.chat_history.append({
            "type": "action",
            "content": "Listed all users",
            "result": result,
            "timestamp": datetime.now()
        })
        st.rerun()
    
    # Manual operations
    st.subheader("Manual Operations")
    
    with st.expander("➕ Create User"):
        with st.form("create_user_form"):
            name = st.text_input("Name")
            email = st.text_input("Email")
            if st.form_submit_button("Create User"):
                if name and email:
                    result = st.session_state.mcp_client.create_user(name, email)
                    st.session_state.chat_history.append({
                        "type": "action",
                        "content": f"Created user: {name}",
                        "result": result,
                        "timestamp": datetime.now()
                    })
                    st.rerun()
                else:
                    st.error("Please provide both name and email")
    
    with st.expander("🔍 Get User"):
        with st.form("get_user_form"):
            user_id = st.number_input("User ID", min_value=1, step=1)
            if st.form_submit_button("Get User"):
                result = st.session_state.mcp_client.get_user(int(user_id))
                st.session_state.chat_history.append({
                    "type": "action",
                    "content": f"Retrieved user ID: {user_id}",
                    "result": result,
                    "timestamp": datetime.now()
                })
                st.rerun()
    
    with st.expander("✏️ Update User"):
        with st.form("update_user_form"):
            user_id = st.number_input("User ID", min_value=1, step=1, key="update_id")
            name = st.text_input("New Name (optional)", key="update_name")
            email = st.text_input("New Email (optional)", key="update_email")
            if st.form_submit_button("Update User"):
                result = st.session_state.mcp_client.update_user(
                    int(user_id), 
                    name if name else None, 
                    email if email else None
                )
                st.session_state.chat_history.append({
                    "type": "action",
                    "content": f"Updated user ID: {user_id}",
                    "result": result,
                    "timestamp": datetime.now()
                })
                st.rerun()
    
    with st.expander("🗑️ Delete User"):
        with st.form("delete_user_form"):
            user_id = st.number_input("User ID", min_value=1, step=1, key="delete_id")
            if st.form_submit_button("Delete User", type="primary"):
                result = st.session_state.mcp_client.delete_user(int(user_id))
                st.session_state.chat_history.append({
                    "type": "action",
                    "content": f"Deleted user ID: {user_id}",
                    "result": result,
                    "timestamp": datetime.now()
                })
                st.rerun()

# Main content area
col1, col2 = st.columns([2, 1])

with col1:
    st.header("💬 Natural Language Interface")
    st.markdown("*Talk to the Google ADK Agent in natural language*")
    
    # Chat interface
    chat_container = st.container()
    
    # Display chat history
    with chat_container:
        for i, chat in enumerate(st.session_state.chat_history[-10:]):  # Show last 10 messages
            if chat["type"] == "user":
                st.chat_message("user").write(chat["content"])
            elif chat["type"] == "assistant":
                with st.chat_message("assistant"):
                    st.write(chat["content"])
                    if "result" in chat and chat["result"]:
                        with st.expander("📊 Detailed Result"):
                            st.json(chat["result"])
            elif chat["type"] == "action":
                with st.chat_message("assistant"):
                    st.write(f"✅ {chat['content']}")
                    if chat["result"]["status"] == "success":
                        st.success("Operation completed successfully!")
                        if "data" in chat["result"]:
                            st.json(chat["result"]["data"])
                    else:
                        st.error(f"Error: {chat['result'].get('message', 'Unknown error')}")
    
    # Chat input
    user_input = st.chat_input("Ask me anything about users... (e.g., 'create user named Alice <NAME_EMAIL>')")
    
    if user_input:
        # Add user message to chat
        st.session_state.chat_history.append({
            "type": "user",
            "content": user_input,
            "timestamp": datetime.now()
        })
        
        # Process with client agent
        with st.spinner("🤖 Processing your request..."):
            result = st.session_state.client_agent.process_natural_language_request(user_input)
        
        # Add assistant response
        response_text = "I've processed your request!"
        if result["status"] == "success":
            if "friendlyMessage" in result:
                response_text = result["friendlyMessage"]
            elif "message" in result:
                response_text = result["message"]
        else:
            response_text = f"❌ {result.get('message', 'Something went wrong')}"
        
        st.session_state.chat_history.append({
            "type": "assistant",
            "content": response_text,
            "result": result,
            "timestamp": datetime.now()
        })
        
        st.rerun()
    
    # Suggestions
    if user_input:
        suggestions = st.session_state.client_agent.get_suggestions(user_input)
        if suggestions:
            st.subheader("💡 Suggestions")
            for suggestion in suggestions:
                if st.button(suggestion, key=f"suggestion_{suggestion}"):
                    # Simulate clicking the suggestion
                    st.session_state.chat_history.append({
                        "type": "user",
                        "content": suggestion,
                        "timestamp": datetime.now()
                    })
                    result = st.session_state.client_agent.process_natural_language_request(suggestion)
                    st.session_state.chat_history.append({
                        "type": "assistant",
                        "content": "I've processed your request!",
                        "result": result,
                        "timestamp": datetime.now()
                    })
                    st.rerun()

with col2:
    st.header("📊 Dashboard")
    
    # Get current users for dashboard
    users_result = st.session_state.mcp_client.list_users()
    
    if users_result["status"] == "success" and "data" in users_result:
        users = users_result["data"]
        
        # User count metric
        st.metric("Total Users", len(users))
        
        # Users table
        if users:
            st.subheader("Current Users")
            df = pd.DataFrame(users)
            st.dataframe(df, use_container_width=True)
            
            # Simple visualization
            if len(users) > 0:
                st.subheader("User Distribution")
                # Create a simple chart showing user creation (mock data for demo)
                chart_data = pd.DataFrame({
                    'User ID': [user['id'] for user in users],
                    'Name Length': [len(user['name']) for user in users]
                })
                fig = px.bar(chart_data, x='User ID', y='Name Length', title='Name Length by User ID')
                st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("No users found. Create some users to see them here!")
    else:
        st.error("Failed to load users for dashboard")
    
    # Conversation summary
    st.subheader("🗣️ Conversation Summary")
    summary = st.session_state.client_agent.get_conversation_summary()
    st.metric("Total Interactions", summary["total_interactions"])
    st.metric("Successful Operations", summary["successful_operations"])

# Footer
st.markdown("---")
st.markdown(
    """
    <div style='text-align: center'>
        <p>🤖 Powered by Google ADK Server Agent | 🔗 Connected via MCP | 🚀 Built with Streamlit</p>
    </div>
    """, 
    unsafe_allow_html=True
)
