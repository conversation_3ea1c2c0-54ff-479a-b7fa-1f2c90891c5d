version: '3.8'

services:
  # Java Spring Boot Application with Google ADK Server Agent
  user-api:
    build:
      context: .
      dockerfile: Dockerfile.java
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/agent/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - user-management-network

  # MCP Server (Python)
  mcp-server:
    build:
      context: ./mcp_server
      dockerfile: Dockerfile
    ports:
      - "8081:8081"
    depends_on:
      user-api:
        condition: service_healthy
    environment:
      - USER_API_URL=http://user-api:8080
    networks:
      - user-management-network

  # Streamlit Application
  streamlit-app:
    build:
      context: ./streamlit_app
      dockerfile: Dockerfile
    ports:
      - "8501:8501"
    depends_on:
      - mcp-server
    environment:
      - MCP_SERVER_URL=http://mcp-server:8081
    networks:
      - user-management-network

networks:
  user-management-network:
    driver: bridge

volumes:
  user-data:
